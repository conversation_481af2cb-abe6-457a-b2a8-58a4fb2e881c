// go run mksyscall_aix_ppc64.go -aix -tags aix,ppc64 syscall_aix.go syscall_aix_ppc64.go
// Code generated by the command above; see README.md. DO NOT EDIT.

//go:build aix && ppc64 && gc

package unix

import (
	"unsafe"
)

//go:cgo_import_dynamic libc_utimes utimes "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_utimensat utimensat "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_getcwd getcwd "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_accept accept "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_getdirent getdirent "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_wait4 wait4 "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_ioctl ioctl "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_fcntl fcntl "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_fsync_range fsync_range "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_acct acct "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_chdir chdir "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_chroot chroot "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_close close "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_dup dup "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_exit exit "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_faccessat faccessat "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_fchdir fchdir "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_fchmod fchmod "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_fchmodat fchmodat "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_fchownat fchownat "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_fdatasync fdatasync "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_getpgid getpgid "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_getpgrp getpgrp "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_getpid getpid "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_getppid getppid "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_getpriority getpriority "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_getrusage getrusage "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_getsid getsid "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_kill kill "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_syslog syslog "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_mkdir mkdir "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_mkdirat mkdirat "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_mkfifo mkfifo "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_mknod mknod "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_mknodat mknodat "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_nanosleep nanosleep "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_open64 open64 "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_openat openat "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_read read "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_readlink readlink "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_renameat renameat "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_setdomainname setdomainname "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_sethostname sethostname "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_setpgid setpgid "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_setsid setsid "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_settimeofday settimeofday "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_setuid setuid "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_setgid setgid "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_setpriority setpriority "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_statx statx "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_sync sync "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_times times "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_umask umask "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_uname uname "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_unlink unlink "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_unlinkat unlinkat "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_ustat ustat "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_write write "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_dup2 dup2 "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_posix_fadvise64 posix_fadvise64 "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_fchown fchown "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_fstat fstat "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_fstatat fstatat "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_fstatfs fstatfs "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_ftruncate ftruncate "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_getegid getegid "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_geteuid geteuid "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_getgid getgid "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_getuid getuid "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_lchown lchown "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_listen listen "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_lstat lstat "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_pause pause "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_pread64 pread64 "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_pwrite64 pwrite64 "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_select select "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_pselect pselect "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_setregid setregid "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_setreuid setreuid "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_shutdown shutdown "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_splice splice "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_stat stat "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_statfs statfs "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_truncate truncate "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_bind bind "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_connect connect "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_getgroups getgroups "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_setgroups setgroups "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_getsockopt getsockopt "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_setsockopt setsockopt "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_socket socket "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_socketpair socketpair "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_getpeername getpeername "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_getsockname getsockname "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_recvfrom recvfrom "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_sendto sendto "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_nrecvmsg nrecvmsg "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_nsendmsg nsendmsg "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_munmap munmap "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_madvise madvise "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_mprotect mprotect "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_mlock mlock "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_mlockall mlockall "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_msync msync "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_munlock munlock "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_munlockall munlockall "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_pipe pipe "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_poll poll "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_gettimeofday gettimeofday "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_time time "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_utime utime "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_getsystemcfg getsystemcfg "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_umount umount "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_getrlimit getrlimit "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_lseek lseek "libc.a/shr_64.o"
//go:cgo_import_dynamic libc_mmap64 mmap64 "libc.a/shr_64.o"

//go:linkname libc_utimes libc_utimes
//go:linkname libc_utimensat libc_utimensat
//go:linkname libc_getcwd libc_getcwd
//go:linkname libc_accept libc_accept
//go:linkname libc_getdirent libc_getdirent
//go:linkname libc_wait4 libc_wait4
//go:linkname libc_ioctl libc_ioctl
//go:linkname libc_fcntl libc_fcntl
//go:linkname libc_fsync_range libc_fsync_range
//go:linkname libc_acct libc_acct
//go:linkname libc_chdir libc_chdir
//go:linkname libc_chroot libc_chroot
//go:linkname libc_close libc_close
//go:linkname libc_dup libc_dup
//go:linkname libc_exit libc_exit
//go:linkname libc_faccessat libc_faccessat
//go:linkname libc_fchdir libc_fchdir
//go:linkname libc_fchmod libc_fchmod
//go:linkname libc_fchmodat libc_fchmodat
//go:linkname libc_fchownat libc_fchownat
//go:linkname libc_fdatasync libc_fdatasync
//go:linkname libc_getpgid libc_getpgid
//go:linkname libc_getpgrp libc_getpgrp
//go:linkname libc_getpid libc_getpid
//go:linkname libc_getppid libc_getppid
//go:linkname libc_getpriority libc_getpriority
//go:linkname libc_getrusage libc_getrusage
//go:linkname libc_getsid libc_getsid
//go:linkname libc_kill libc_kill
//go:linkname libc_syslog libc_syslog
//go:linkname libc_mkdir libc_mkdir
//go:linkname libc_mkdirat libc_mkdirat
//go:linkname libc_mkfifo libc_mkfifo
//go:linkname libc_mknod libc_mknod
//go:linkname libc_mknodat libc_mknodat
//go:linkname libc_nanosleep libc_nanosleep
//go:linkname libc_open64 libc_open64
//go:linkname libc_openat libc_openat
//go:linkname libc_read libc_read
//go:linkname libc_readlink libc_readlink
//go:linkname libc_renameat libc_renameat
//go:linkname libc_setdomainname libc_setdomainname
//go:linkname libc_sethostname libc_sethostname
//go:linkname libc_setpgid libc_setpgid
//go:linkname libc_setsid libc_setsid
//go:linkname libc_settimeofday libc_settimeofday
//go:linkname libc_setuid libc_setuid
//go:linkname libc_setgid libc_setgid
//go:linkname libc_setpriority libc_setpriority
//go:linkname libc_statx libc_statx
//go:linkname libc_sync libc_sync
//go:linkname libc_times libc_times
//go:linkname libc_umask libc_umask
//go:linkname libc_uname libc_uname
//go:linkname libc_unlink libc_unlink
//go:linkname libc_unlinkat libc_unlinkat
//go:linkname libc_ustat libc_ustat
//go:linkname libc_write libc_write
//go:linkname libc_dup2 libc_dup2
//go:linkname libc_posix_fadvise64 libc_posix_fadvise64
//go:linkname libc_fchown libc_fchown
//go:linkname libc_fstat libc_fstat
//go:linkname libc_fstatat libc_fstatat
//go:linkname libc_fstatfs libc_fstatfs
//go:linkname libc_ftruncate libc_ftruncate
//go:linkname libc_getegid libc_getegid
//go:linkname libc_geteuid libc_geteuid
//go:linkname libc_getgid libc_getgid
//go:linkname libc_getuid libc_getuid
//go:linkname libc_lchown libc_lchown
//go:linkname libc_listen libc_listen
//go:linkname libc_lstat libc_lstat
//go:linkname libc_pause libc_pause
//go:linkname libc_pread64 libc_pread64
//go:linkname libc_pwrite64 libc_pwrite64
//go:linkname libc_select libc_select
//go:linkname libc_pselect libc_pselect
//go:linkname libc_setregid libc_setregid
//go:linkname libc_setreuid libc_setreuid
//go:linkname libc_shutdown libc_shutdown
//go:linkname libc_splice libc_splice
//go:linkname libc_stat libc_stat
//go:linkname libc_statfs libc_statfs
//go:linkname libc_truncate libc_truncate
//go:linkname libc_bind libc_bind
//go:linkname libc_connect libc_connect
//go:linkname libc_getgroups libc_getgroups
//go:linkname libc_setgroups libc_setgroups
//go:linkname libc_getsockopt libc_getsockopt
//go:linkname libc_setsockopt libc_setsockopt
//go:linkname libc_socket libc_socket
//go:linkname libc_socketpair libc_socketpair
//go:linkname libc_getpeername libc_getpeername
//go:linkname libc_getsockname libc_getsockname
//go:linkname libc_recvfrom libc_recvfrom
//go:linkname libc_sendto libc_sendto
//go:linkname libc_nrecvmsg libc_nrecvmsg
//go:linkname libc_nsendmsg libc_nsendmsg
//go:linkname libc_munmap libc_munmap
//go:linkname libc_madvise libc_madvise
//go:linkname libc_mprotect libc_mprotect
//go:linkname libc_mlock libc_mlock
//go:linkname libc_mlockall libc_mlockall
//go:linkname libc_msync libc_msync
//go:linkname libc_munlock libc_munlock
//go:linkname libc_munlockall libc_munlockall
//go:linkname libc_pipe libc_pipe
//go:linkname libc_poll libc_poll
//go:linkname libc_gettimeofday libc_gettimeofday
//go:linkname libc_time libc_time
//go:linkname libc_utime libc_utime
//go:linkname libc_getsystemcfg libc_getsystemcfg
//go:linkname libc_umount libc_umount
//go:linkname libc_getrlimit libc_getrlimit
//go:linkname libc_lseek libc_lseek
//go:linkname libc_mmap64 libc_mmap64

type syscallFunc uintptr

var (
	libc_utimes,
	libc_utimensat,
	libc_getcwd,
	libc_accept,
	libc_getdirent,
	libc_wait4,
	libc_ioctl,
	libc_fcntl,
	libc_fsync_range,
	libc_acct,
	libc_chdir,
	libc_chroot,
	libc_close,
	libc_dup,
	libc_exit,
	libc_faccessat,
	libc_fchdir,
	libc_fchmod,
	libc_fchmodat,
	libc_fchownat,
	libc_fdatasync,
	libc_getpgid,
	libc_getpgrp,
	libc_getpid,
	libc_getppid,
	libc_getpriority,
	libc_getrusage,
	libc_getsid,
	libc_kill,
	libc_syslog,
	libc_mkdir,
	libc_mkdirat,
	libc_mkfifo,
	libc_mknod,
	libc_mknodat,
	libc_nanosleep,
	libc_open64,
	libc_openat,
	libc_read,
	libc_readlink,
	libc_renameat,
	libc_setdomainname,
	libc_sethostname,
	libc_setpgid,
	libc_setsid,
	libc_settimeofday,
	libc_setuid,
	libc_setgid,
	libc_setpriority,
	libc_statx,
	libc_sync,
	libc_times,
	libc_umask,
	libc_uname,
	libc_unlink,
	libc_unlinkat,
	libc_ustat,
	libc_write,
	libc_dup2,
	libc_posix_fadvise64,
	libc_fchown,
	libc_fstat,
	libc_fstatat,
	libc_fstatfs,
	libc_ftruncate,
	libc_getegid,
	libc_geteuid,
	libc_getgid,
	libc_getuid,
	libc_lchown,
	libc_listen,
	libc_lstat,
	libc_pause,
	libc_pread64,
	libc_pwrite64,
	libc_select,
	libc_pselect,
	libc_setregid,
	libc_setreuid,
	libc_shutdown,
	libc_splice,
	libc_stat,
	libc_statfs,
	libc_truncate,
	libc_bind,
	libc_connect,
	libc_getgroups,
	libc_setgroups,
	libc_getsockopt,
	libc_setsockopt,
	libc_socket,
	libc_socketpair,
	libc_getpeername,
	libc_getsockname,
	libc_recvfrom,
	libc_sendto,
	libc_nrecvmsg,
	libc_nsendmsg,
	libc_munmap,
	libc_madvise,
	libc_mprotect,
	libc_mlock,
	libc_mlockall,
	libc_msync,
	libc_munlock,
	libc_munlockall,
	libc_pipe,
	libc_poll,
	libc_gettimeofday,
	libc_time,
	libc_utime,
	libc_getsystemcfg,
	libc_umount,
	libc_getrlimit,
	libc_lseek,
	libc_mmap64 syscallFunc
)

// Implemented in runtime/syscall_aix.go.
func rawSyscall6(trap, nargs, a1, a2, a3, a4, a5, a6 uintptr) (r1, r2 uintptr, err Errno)
func syscall6(trap, nargs, a1, a2, a3, a4, a5, a6 uintptr) (r1, r2 uintptr, err Errno)

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callutimes(_p0 uintptr, times uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_utimes)), 2, _p0, times, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callutimensat(dirfd int, _p0 uintptr, times uintptr, flag int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_utimensat)), 4, uintptr(dirfd), _p0, times, uintptr(flag), 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callgetcwd(_p0 uintptr, _lenp0 int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_getcwd)), 2, _p0, uintptr(_lenp0), 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callaccept(s int, rsa uintptr, addrlen uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_accept)), 3, uintptr(s), rsa, addrlen, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callgetdirent(fd int, _p0 uintptr, _lenp0 int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_getdirent)), 3, uintptr(fd), _p0, uintptr(_lenp0), 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callwait4(pid int, status uintptr, options int, rusage uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_wait4)), 4, uintptr(pid), status, uintptr(options), rusage, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callioctl(fd int, req int, arg uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_ioctl)), 3, uintptr(fd), uintptr(req), arg, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callioctl_ptr(fd int, req int, arg unsafe.Pointer) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_ioctl)), 3, uintptr(fd), uintptr(req), uintptr(arg), 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callfcntl(fd uintptr, cmd int, arg uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_fcntl)), 3, fd, uintptr(cmd), arg, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callfsync_range(fd int, how int, start int64, length int64) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_fsync_range)), 4, uintptr(fd), uintptr(how), uintptr(start), uintptr(length), 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callacct(_p0 uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_acct)), 1, _p0, 0, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callchdir(_p0 uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_chdir)), 1, _p0, 0, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callchroot(_p0 uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_chroot)), 1, _p0, 0, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callclose(fd int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_close)), 1, uintptr(fd), 0, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func calldup(oldfd int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_dup)), 1, uintptr(oldfd), 0, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callexit(code int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_exit)), 1, uintptr(code), 0, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callfaccessat(dirfd int, _p0 uintptr, mode uint32, flags int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_faccessat)), 4, uintptr(dirfd), _p0, uintptr(mode), uintptr(flags), 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callfchdir(fd int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_fchdir)), 1, uintptr(fd), 0, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callfchmod(fd int, mode uint32) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_fchmod)), 2, uintptr(fd), uintptr(mode), 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callfchmodat(dirfd int, _p0 uintptr, mode uint32, flags int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_fchmodat)), 4, uintptr(dirfd), _p0, uintptr(mode), uintptr(flags), 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callfchownat(dirfd int, _p0 uintptr, uid int, gid int, flags int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_fchownat)), 5, uintptr(dirfd), _p0, uintptr(uid), uintptr(gid), uintptr(flags), 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callfdatasync(fd int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_fdatasync)), 1, uintptr(fd), 0, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callgetpgid(pid int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = rawSyscall6(uintptr(unsafe.Pointer(&libc_getpgid)), 1, uintptr(pid), 0, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callgetpgrp() (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_getpgrp)), 0, 0, 0, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callgetpid() (r1 uintptr, e1 Errno) {
	r1, _, e1 = rawSyscall6(uintptr(unsafe.Pointer(&libc_getpid)), 0, 0, 0, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callgetppid() (r1 uintptr, e1 Errno) {
	r1, _, e1 = rawSyscall6(uintptr(unsafe.Pointer(&libc_getppid)), 0, 0, 0, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callgetpriority(which int, who int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_getpriority)), 2, uintptr(which), uintptr(who), 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callgetrusage(who int, rusage uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = rawSyscall6(uintptr(unsafe.Pointer(&libc_getrusage)), 2, uintptr(who), rusage, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callgetsid(pid int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = rawSyscall6(uintptr(unsafe.Pointer(&libc_getsid)), 1, uintptr(pid), 0, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callkill(pid int, sig int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = rawSyscall6(uintptr(unsafe.Pointer(&libc_kill)), 2, uintptr(pid), uintptr(sig), 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callsyslog(typ int, _p0 uintptr, _lenp0 int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_syslog)), 3, uintptr(typ), _p0, uintptr(_lenp0), 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callmkdir(dirfd int, _p0 uintptr, mode uint32) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_mkdir)), 3, uintptr(dirfd), _p0, uintptr(mode), 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callmkdirat(dirfd int, _p0 uintptr, mode uint32) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_mkdirat)), 3, uintptr(dirfd), _p0, uintptr(mode), 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callmkfifo(_p0 uintptr, mode uint32) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_mkfifo)), 2, _p0, uintptr(mode), 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callmknod(_p0 uintptr, mode uint32, dev int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_mknod)), 3, _p0, uintptr(mode), uintptr(dev), 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callmknodat(dirfd int, _p0 uintptr, mode uint32, dev int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_mknodat)), 4, uintptr(dirfd), _p0, uintptr(mode), uintptr(dev), 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callnanosleep(time uintptr, leftover uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_nanosleep)), 2, time, leftover, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callopen64(_p0 uintptr, mode int, perm uint32) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_open64)), 3, _p0, uintptr(mode), uintptr(perm), 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callopenat(dirfd int, _p0 uintptr, flags int, mode uint32) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_openat)), 4, uintptr(dirfd), _p0, uintptr(flags), uintptr(mode), 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callread(fd int, _p0 uintptr, _lenp0 int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_read)), 3, uintptr(fd), _p0, uintptr(_lenp0), 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callreadlink(_p0 uintptr, _p1 uintptr, _lenp1 int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_readlink)), 3, _p0, _p1, uintptr(_lenp1), 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callrenameat(olddirfd int, _p0 uintptr, newdirfd int, _p1 uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_renameat)), 4, uintptr(olddirfd), _p0, uintptr(newdirfd), _p1, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callsetdomainname(_p0 uintptr, _lenp0 int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_setdomainname)), 2, _p0, uintptr(_lenp0), 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callsethostname(_p0 uintptr, _lenp0 int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_sethostname)), 2, _p0, uintptr(_lenp0), 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callsetpgid(pid int, pgid int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = rawSyscall6(uintptr(unsafe.Pointer(&libc_setpgid)), 2, uintptr(pid), uintptr(pgid), 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callsetsid() (r1 uintptr, e1 Errno) {
	r1, _, e1 = rawSyscall6(uintptr(unsafe.Pointer(&libc_setsid)), 0, 0, 0, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callsettimeofday(tv uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = rawSyscall6(uintptr(unsafe.Pointer(&libc_settimeofday)), 1, tv, 0, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callsetuid(uid int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_setuid)), 1, uintptr(uid), 0, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callsetgid(uid int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_setgid)), 1, uintptr(uid), 0, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callsetpriority(which int, who int, prio int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_setpriority)), 3, uintptr(which), uintptr(who), uintptr(prio), 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callstatx(dirfd int, _p0 uintptr, flags int, mask int, stat uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_statx)), 5, uintptr(dirfd), _p0, uintptr(flags), uintptr(mask), stat, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callsync() (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_sync)), 0, 0, 0, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func calltimes(tms uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = rawSyscall6(uintptr(unsafe.Pointer(&libc_times)), 1, tms, 0, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callumask(mask int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = rawSyscall6(uintptr(unsafe.Pointer(&libc_umask)), 1, uintptr(mask), 0, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func calluname(buf uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = rawSyscall6(uintptr(unsafe.Pointer(&libc_uname)), 1, buf, 0, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callunlink(_p0 uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_unlink)), 1, _p0, 0, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callunlinkat(dirfd int, _p0 uintptr, flags int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_unlinkat)), 3, uintptr(dirfd), _p0, uintptr(flags), 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callustat(dev int, ubuf uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_ustat)), 2, uintptr(dev), ubuf, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callwrite(fd int, _p0 uintptr, _lenp0 int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_write)), 3, uintptr(fd), _p0, uintptr(_lenp0), 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func calldup2(oldfd int, newfd int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_dup2)), 2, uintptr(oldfd), uintptr(newfd), 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callposix_fadvise64(fd int, offset int64, length int64, advice int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_posix_fadvise64)), 4, uintptr(fd), uintptr(offset), uintptr(length), uintptr(advice), 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callfchown(fd int, uid int, gid int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_fchown)), 3, uintptr(fd), uintptr(uid), uintptr(gid), 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callfstat(fd int, stat uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_fstat)), 2, uintptr(fd), stat, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callfstatat(dirfd int, _p0 uintptr, stat uintptr, flags int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_fstatat)), 4, uintptr(dirfd), _p0, stat, uintptr(flags), 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callfstatfs(fd int, buf uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_fstatfs)), 2, uintptr(fd), buf, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callftruncate(fd int, length int64) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_ftruncate)), 2, uintptr(fd), uintptr(length), 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callgetegid() (r1 uintptr, e1 Errno) {
	r1, _, e1 = rawSyscall6(uintptr(unsafe.Pointer(&libc_getegid)), 0, 0, 0, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callgeteuid() (r1 uintptr, e1 Errno) {
	r1, _, e1 = rawSyscall6(uintptr(unsafe.Pointer(&libc_geteuid)), 0, 0, 0, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callgetgid() (r1 uintptr, e1 Errno) {
	r1, _, e1 = rawSyscall6(uintptr(unsafe.Pointer(&libc_getgid)), 0, 0, 0, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callgetuid() (r1 uintptr, e1 Errno) {
	r1, _, e1 = rawSyscall6(uintptr(unsafe.Pointer(&libc_getuid)), 0, 0, 0, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func calllchown(_p0 uintptr, uid int, gid int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_lchown)), 3, _p0, uintptr(uid), uintptr(gid), 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func calllisten(s int, n int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_listen)), 2, uintptr(s), uintptr(n), 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func calllstat(_p0 uintptr, stat uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_lstat)), 2, _p0, stat, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callpause() (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_pause)), 0, 0, 0, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callpread64(fd int, _p0 uintptr, _lenp0 int, offset int64) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_pread64)), 4, uintptr(fd), _p0, uintptr(_lenp0), uintptr(offset), 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callpwrite64(fd int, _p0 uintptr, _lenp0 int, offset int64) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_pwrite64)), 4, uintptr(fd), _p0, uintptr(_lenp0), uintptr(offset), 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callselect(nfd int, r uintptr, w uintptr, e uintptr, timeout uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_select)), 5, uintptr(nfd), r, w, e, timeout, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callpselect(nfd int, r uintptr, w uintptr, e uintptr, timeout uintptr, sigmask uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_pselect)), 6, uintptr(nfd), r, w, e, timeout, sigmask)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callsetregid(rgid int, egid int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = rawSyscall6(uintptr(unsafe.Pointer(&libc_setregid)), 2, uintptr(rgid), uintptr(egid), 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callsetreuid(ruid int, euid int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = rawSyscall6(uintptr(unsafe.Pointer(&libc_setreuid)), 2, uintptr(ruid), uintptr(euid), 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callshutdown(fd int, how int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_shutdown)), 2, uintptr(fd), uintptr(how), 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callsplice(rfd int, roff uintptr, wfd int, woff uintptr, len int, flags int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_splice)), 6, uintptr(rfd), roff, uintptr(wfd), woff, uintptr(len), uintptr(flags))
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callstat(_p0 uintptr, statptr uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_stat)), 2, _p0, statptr, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callstatfs(_p0 uintptr, buf uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_statfs)), 2, _p0, buf, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func calltruncate(_p0 uintptr, length int64) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_truncate)), 2, _p0, uintptr(length), 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callbind(s int, addr uintptr, addrlen uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_bind)), 3, uintptr(s), addr, addrlen, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callconnect(s int, addr uintptr, addrlen uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_connect)), 3, uintptr(s), addr, addrlen, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callgetgroups(n int, list uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = rawSyscall6(uintptr(unsafe.Pointer(&libc_getgroups)), 2, uintptr(n), list, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callsetgroups(n int, list uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = rawSyscall6(uintptr(unsafe.Pointer(&libc_setgroups)), 2, uintptr(n), list, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callgetsockopt(s int, level int, name int, val uintptr, vallen uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_getsockopt)), 5, uintptr(s), uintptr(level), uintptr(name), val, vallen, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callsetsockopt(s int, level int, name int, val uintptr, vallen uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_setsockopt)), 5, uintptr(s), uintptr(level), uintptr(name), val, vallen, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callsocket(domain int, typ int, proto int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = rawSyscall6(uintptr(unsafe.Pointer(&libc_socket)), 3, uintptr(domain), uintptr(typ), uintptr(proto), 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callsocketpair(domain int, typ int, proto int, fd uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = rawSyscall6(uintptr(unsafe.Pointer(&libc_socketpair)), 4, uintptr(domain), uintptr(typ), uintptr(proto), fd, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callgetpeername(fd int, rsa uintptr, addrlen uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = rawSyscall6(uintptr(unsafe.Pointer(&libc_getpeername)), 3, uintptr(fd), rsa, addrlen, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callgetsockname(fd int, rsa uintptr, addrlen uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = rawSyscall6(uintptr(unsafe.Pointer(&libc_getsockname)), 3, uintptr(fd), rsa, addrlen, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callrecvfrom(fd int, _p0 uintptr, _lenp0 int, flags int, from uintptr, fromlen uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_recvfrom)), 6, uintptr(fd), _p0, uintptr(_lenp0), uintptr(flags), from, fromlen)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callsendto(s int, _p0 uintptr, _lenp0 int, flags int, to uintptr, addrlen uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_sendto)), 6, uintptr(s), _p0, uintptr(_lenp0), uintptr(flags), to, addrlen)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callnrecvmsg(s int, msg uintptr, flags int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_nrecvmsg)), 3, uintptr(s), msg, uintptr(flags), 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callnsendmsg(s int, msg uintptr, flags int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_nsendmsg)), 3, uintptr(s), msg, uintptr(flags), 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callmunmap(addr uintptr, length uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_munmap)), 2, addr, length, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callmadvise(_p0 uintptr, _lenp0 int, advice int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_madvise)), 3, _p0, uintptr(_lenp0), uintptr(advice), 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callmprotect(_p0 uintptr, _lenp0 int, prot int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_mprotect)), 3, _p0, uintptr(_lenp0), uintptr(prot), 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callmlock(_p0 uintptr, _lenp0 int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_mlock)), 2, _p0, uintptr(_lenp0), 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callmlockall(flags int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_mlockall)), 1, uintptr(flags), 0, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callmsync(_p0 uintptr, _lenp0 int, flags int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_msync)), 3, _p0, uintptr(_lenp0), uintptr(flags), 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callmunlock(_p0 uintptr, _lenp0 int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_munlock)), 2, _p0, uintptr(_lenp0), 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callmunlockall() (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_munlockall)), 0, 0, 0, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callpipe(p uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = rawSyscall6(uintptr(unsafe.Pointer(&libc_pipe)), 1, p, 0, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callpoll(fds uintptr, nfds int, timeout int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_poll)), 3, fds, uintptr(nfds), uintptr(timeout), 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callgettimeofday(tv uintptr, tzp uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_gettimeofday)), 2, tv, tzp, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func calltime(t uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = rawSyscall6(uintptr(unsafe.Pointer(&libc_time)), 1, t, 0, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callutime(_p0 uintptr, buf uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_utime)), 2, _p0, buf, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callgetsystemcfg(label int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_getsystemcfg)), 1, uintptr(label), 0, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callumount(_p0 uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_umount)), 1, _p0, 0, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callgetrlimit(resource int, rlim uintptr) (r1 uintptr, e1 Errno) {
	r1, _, e1 = rawSyscall6(uintptr(unsafe.Pointer(&libc_getrlimit)), 2, uintptr(resource), rlim, 0, 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func calllseek(fd int, offset int64, whence int) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_lseek)), 3, uintptr(fd), uintptr(offset), uintptr(whence), 0, 0, 0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func callmmap64(addr uintptr, length uintptr, prot int, flags int, fd int, offset int64) (r1 uintptr, e1 Errno) {
	r1, _, e1 = syscall6(uintptr(unsafe.Pointer(&libc_mmap64)), 6, addr, length, uintptr(prot), uintptr(flags), uintptr(fd), uintptr(offset))
	return
}
