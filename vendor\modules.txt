# github.com/StackExchange/wmi v1.2.1
## explicit; go 1.13
github.com/StackExchange/wmi
# github.com/beevik/ntp v1.4.3
## explicit; go 1.17
github.com/beevik/ntp
# github.com/go-ole/go-ole v1.2.6
## explicit; go 1.12
github.com/go-ole/go-ole
github.com/go-ole/go-ole/oleutil
# github.com/lufia/plan9stats v0.0.0-20211012122336-39d0f177ccd0
## explicit; go 1.16
github.com/lufia/plan9stats
# github.com/power-devops/perfstat v0.0.0-20210106213030-5aafc221ea8c
## explicit; go 1.14
github.com/power-devops/perfstat
# github.com/shirou/gopsutil/v3 v3.24.5
## explicit; go 1.18
github.com/shirou/gopsutil/v3/common
github.com/shirou/gopsutil/v3/cpu
github.com/shirou/gopsutil/v3/disk
github.com/shirou/gopsutil/v3/host
github.com/shirou/gopsutil/v3/internal/common
github.com/shirou/gopsutil/v3/mem
github.com/shirou/gopsutil/v3/net
github.com/shirou/gopsutil/v3/process
# github.com/shoenig/go-m1cpu v0.1.6
## explicit; go 1.20
github.com/shoenig/go-m1cpu
# github.com/tklauser/go-sysconf v0.3.12
## explicit; go 1.13
github.com/tklauser/go-sysconf
# github.com/tklauser/numcpus v0.6.1
## explicit; go 1.13
github.com/tklauser/numcpus
# github.com/yusufpapurcu/wmi v1.2.4
## explicit; go 1.16
github.com/yusufpapurcu/wmi
# golang.org/x/net v0.25.0
## explicit; go 1.18
golang.org/x/net/bpf
golang.org/x/net/internal/iana
golang.org/x/net/internal/socket
golang.org/x/net/ipv4
# golang.org/x/sys v0.20.0
## explicit; go 1.18
golang.org/x/sys/unix
golang.org/x/sys/windows
golang.org/x/sys/windows/registry
