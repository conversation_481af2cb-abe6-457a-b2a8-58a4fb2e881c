package main

import (
	licenense "auth/Licenense"
	"flag"
	"fmt"
	// 第三方NTP库
)

// printUsage 打印使用说明
func printUsage() {
	fmt.Println("授权管理工具")
	fmt.Println("用法:")
	fmt.Println("  生成授权: ./auth -mode=generate -config=<配置文件>")
	fmt.Println("  验证授权: ./auth -mode=verify -file=<授权文件>")
	fmt.Println("  查看主机信息: ./auth -mode=info")
	fmt.Println("  创建示例配置: ./auth -mode=sample -config=<配置文件名>")
	fmt.Println("  创建配置模板: ./auth -mode=template -type=<模板类型> -config=<配置文件名>")
	fmt.Println("")
	fmt.Println("参数说明:")
	fmt.Println("  -config=<文件名>      配置文件路径 (generate/sample/template模式必需)")
	fmt.Println("  -file=<文件名>        授权文件路径 (verify模式必需)")
	fmt.Println("  -type=<模板类型>      模板类型 (template模式必需): trial, full, enterprise")
	fmt.Println("")
	fmt.Println("配置文件格式 (JSON):")
	fmt.Println(`  {
    "user_name": "用户名",
    "company": "公司名",
    "product_name": "产品名",
    "developer_name": "开发者名",
    "version": "版本号",
    "is_trial": true,
    "valid_years": 1,
    "trial_duration_days": 30,
    "max_hosts": 2,
    "feature": {
      "client_features": [{"name": "功能名", "desc": "功能描述", "allowed": true}],
      "server_features": [{"name": "服务名", "desc": "服务描述", "allowed": true}]
    },
    "supported_os": ["windows", "linux", "darwin"],
    "output_file": "license.json",
    "register_host": true
  }`)
	fmt.Println("")
	fmt.Println("支持的操作系统: windows, linux, darwin, freebsd, all")
	fmt.Println("支持的模板类型: trial(试用版), full(正式版), enterprise(企业版)")
	fmt.Println("")
	fmt.Println("示例:")
	fmt.Println("  ./auth -mode=sample -config=config.json")
	fmt.Println("  ./auth -mode=template -type=trial -config=trial_config.json")
	fmt.Println("  ./auth -mode=template -type=enterprise -config=enterprise_config.json")
	fmt.Println("  ./auth -mode=generate -config=config.json")
	fmt.Println("  ./auth -mode=verify -file=license.json")
	fmt.Println("  ./auth -mode=info")
}

func main() {
	// 定义命令行参数
	mode := flag.String("mode", "", "操作模式: generate(生成授权), verify(验证授权), info(查看主机信息), sample(创建示例配置), template(创建配置模板)")
	file := flag.String("file", "license.json", "授权文件路径 (verify模式必需)")
	configFile := flag.String("config", "jma-product-base-config.json", "配置文件路径 (generate/sample/template模式必需)")
	keyFile := flag.String("key", "keys.json", "密钥文件 (生成授权、验证授权、必需)")
	templateType := flag.String("type", "trial", "模板类型 (template模式必需): trial, full, enterprise")

	flag.Parse()

	// var servers = []string{
	// 	"pool.ntp.org",
	// 	"time.apple.com:123",
	// 	"ntp.aliyun.com:123",
	// 	"time.windows.com:123",
	// 	"ntp.ntsc.ac.cn:123", // 中国国家授时中心
	// }
	// for _, server := range servers {
	// 	response, err := ntp.Query(server)
	// 	if err != nil {
	// 		fmt.Println("NTP请求失败:", err)
	// 		continue
	// 	}

	// 	// 计算网络延迟后的精确时间
	// 	exactTime := time.Now().Add(response.ClockOffset)
	// 	fmt.Println("精确网络时间(UTC):", exactTime.UTC().Format(time.RFC3339Nano))
	// 	fmt.Println("本地时间:", exactTime.Local().Format("2006-01-02 15:04:05.999999999 -07:00"))

	// }

	// tm := licenense.GetTimeFromNet()
	// fmt.Println("本地时间:", tm.Local().Format("2006-01-02 15:04:05.999999999 -07:00"))

	// 检查模式参数
	if *mode == "" {
		printUsage()
		return
	}

	switch *mode {
	case "info":
		licenense.ShowHostInfo()

	case "generate":
		if *configFile == "" {
			fmt.Println("错误: generate模式需要指定 -config 参数")
			printUsage()
			return
		}
		config, err := licenense.LoadLicenseConfig(*configFile)
		if err != nil {
			fmt.Printf("加载配置文件失败: %v\n", err)
			return
		}
		// 创建授权管理器
		manager := licenense.NewLicenseManager(*keyFile)

		licenense.GenerateLicenseFromConfig(manager, config)

	case "verify":
		if *file == "" {
			fmt.Println("错误: verify模式需要指定 -file 参数")
			printUsage()
			return
		}
		// 创建授权管理器
		manager := licenense.NewLicenseManager(*keyFile)

		fmt.Println(*file)
		err := licenense.VerifyLicense(manager, *file)
		if err != nil {
			fmt.Printf("授权验证失败: %v\n", err)
		}

	case "sample":
		if *configFile == "" {
			fmt.Println("错误: sample模式需要指定 -config 参数")
			printUsage()
			return
		}

		err := licenense.CreateSampleConfig(*configFile)
		if err != nil {
			fmt.Printf("创建示例配置失败: %v\n", err)
		}

	case "template":
		if *configFile == "" {
			fmt.Println("错误: template模式需要指定 -config 参数")
			printUsage()
			return
		}
		if *templateType == "" {
			fmt.Println("错误: template模式需要指定 -type 参数")
			printUsage()
			return
		}
		err := licenense.CreateConfigTemplate(*templateType, *configFile)
		if err != nil {
			fmt.Printf("创建配置模板失败: %v\n", err)
		}

	default:
		fmt.Printf("错误: 未知的模式 '%s'\n\n", *mode)
		printUsage()
	}
}
