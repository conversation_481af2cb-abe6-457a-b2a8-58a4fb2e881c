package main

import (
	licenense "auth/Licenense"
	"fmt"
	"log"
	"time"
)

func main() {
	fmt.Println("=== 测试完善的 LicenseInfo 功能 ===\n")

	// 1. 创建试用授权
	fmt.Println("1. 创建试用授权:")
	trialFeatures := []string{"基础功能", "高级功能", "数据服务"}
	supportedOS := []licenense.OSType{licenense.OSWindows, licenense.OSLinux}
	
	trialLicense := licenense.CreateTrialLicense(
		"张三", "测试公司", "测试产品", "v1.0.0",
		2, trialFeatures, supportedOS, 30,
	)
	
	fmt.Printf("试用授权创建成功:\n")
	fmt.Printf("  授权ID: %s\n", trialLicense.LicenseID)
	fmt.Printf("  摘要: %s\n", trialLicense.GetSummary())
	fmt.Printf("  功能列表: %v\n", trialLicense.GetFeatureNames())
	fmt.Printf("  距离过期: %d 天\n", trialLicense.DaysUntilExpiry())
	fmt.Printf("  是否活跃: %t\n", trialLicense.IsActive())

	// 2. 测试主机注册功能
	fmt.Println("\n2. 测试主机注册功能:")
	
	// 创建主机信息
	host1 := licenense.HostInfo{
		Fingerprint: "HOST-001-FINGERPRINT",
		OSType:      licenense.OSWindows,
		OSVersion:   "Windows 11",
		Hostname:    "DESKTOP-001",
	}
	
	host2 := licenense.HostInfo{
		Fingerprint: "HOST-002-FINGERPRINT",
		OSType:      licenense.OSLinux,
		OSVersion:   "Ubuntu 22.04",
		Hostname:    "SERVER-001",
	}
	
	// 注册第一台主机
	err := trialLicense.AddRegisteredHost(host1)
	if err != nil {
		log.Printf("注册主机1失败: %v", err)
	} else {
		fmt.Printf("✓ 主机1注册成功: %s\n", host1.Hostname)
	}
	
	// 注册第二台主机
	err = trialLicense.AddRegisteredHost(host2)
	if err != nil {
		log.Printf("注册主机2失败: %v", err)
	} else {
		fmt.Printf("✓ 主机2注册成功: %s\n", host2.Hostname)
	}
	
	fmt.Printf("  剩余可注册主机数: %d\n", trialLicense.GetRemainingHosts())
	fmt.Printf("  是否可以注册新主机: %t\n", trialLicense.CanRegisterHost())

	// 3. 测试功能检查
	fmt.Println("\n3. 测试功能检查:")
	
	testFeatures := []string{"基础功能", "高级功能", "专业功能", "数据服务"}
	for _, feature := range testFeatures {
		hasFeature := trialLicense.HasFeature(feature)
		status := "❌"
		if hasFeature {
			status = "✅"
		}
		fmt.Printf("  %s %s\n", status, feature)
	}

	// 4. 创建正式授权
	fmt.Println("\n4. 创建正式授权:")
	
	fullFeatures := []string{"基础功能", "高级功能", "专业功能", "企业功能", "数据服务", "管理服务"}
	fullLicense := licenense.CreateFullLicense(
		"李四", "正式公司", "企业产品", "v2.0.0",
		10, fullFeatures, 3, supportedOS,
	)
	
	fmt.Printf("正式授权创建成功:\n")
	fmt.Printf("  授权ID: %s\n", fullLicense.LicenseID)
	fmt.Printf("  摘要: %s\n", fullLicense.GetSummary())
	fmt.Printf("  功能列表: %v\n", fullLicense.GetFeatureNames())

	// 5. 测试状态更新
	fmt.Println("\n5. 测试状态更新:")
	
	fmt.Printf("更新前状态: %s\n", trialLicense.Status)
	trialLicense.UpdateStatus("suspended", "暂停使用进行维护")
	fmt.Printf("更新后状态: %s\n", trialLicense.Status)
	fmt.Printf("备注: %s\n", trialLicense.Notes)
	fmt.Printf("是否活跃: %t\n", trialLicense.IsActive())

	// 6. 测试主机管理
	fmt.Println("\n6. 测试主机管理:")
	
	fmt.Printf("注册的主机数量: %d\n", len(trialLicense.RegisteredHosts))
	for i, host := range trialLicense.RegisteredHosts {
		fmt.Printf("  主机%d: %s (%s) - %s\n", i+1, host.Hostname, host.OSType, host.Fingerprint)
	}
	
	// 移除一台主机
	err = trialLicense.RemoveRegisteredHost("HOST-001-FINGERPRINT")
	if err != nil {
		log.Printf("移除主机失败: %v", err)
	} else {
		fmt.Printf("✓ 主机移除成功\n")
	}
	
	fmt.Printf("移除后剩余主机数: %d\n", len(trialLicense.RegisteredHosts))
	fmt.Printf("剩余可注册主机数: %d\n", trialLicense.GetRemainingHosts())

	// 7. 测试过期检查
	fmt.Println("\n7. 测试过期检查:")
	
	// 创建一个已过期的授权进行测试
	expiredLicense := licenense.CreateTrialLicense(
		"过期用户", "过期公司", "过期产品", "v0.1.0",
		1, []string{"基础功能"}, supportedOS, -1, // -1天表示昨天过期
	)
	
	fmt.Printf("过期授权测试:\n")
	fmt.Printf("  是否过期: %t\n", expiredLicense.IsExpired())
	fmt.Printf("  是否活跃: %t\n", expiredLicense.IsActive())
	fmt.Printf("  距离过期天数: %d\n", expiredLicense.DaysUntilExpiry())

	fmt.Println("\n=== 测试完成 ===")
}
