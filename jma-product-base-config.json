{"user_name": "试用用户", "company": "试用公司", "product_name": "军密安敏感信息检查管控系统平台", "developer_name": "北京军密安信息技术有限公司", "version": "v1.0.0", "is_trial": true, "valid_years": 0, "trial_duration_days": 30, "max_hosts": 10, "features": {"client_features": [{"name": "文件内容检查", "desc": "支持办公文档的文件名、文件内容检查，支持多关键字组合过滤、文件时间、文件大小等条件进行筛选", "allowed": true}, {"name": "文件痕迹检查", "desc": "操作系统对文件使用的历史信息，检查出文件操作操作记录，支持多关键字组合过滤、文件时间、文件大小等条件进行筛选", "allowed": true}, {"name": "文件痕迹检查", "desc": "操作系统对文件使用的历史信息，检查出文件操作操作记录，支持多关键字组合过滤、文件时间、文件大小等条件进行筛选", "allowed": true}, {"name": "已删除文件检查", "desc": "检查操作系统删除的的文件，支持多关键字组合过滤、文件时间、文件大小等条件进行筛选", "allowed": true}], "server_features": [{"name": "首页", "desc": "首页功能，展示检查的总体统计信息", "allowed": true}, {"name": "主机管理", "desc": "主机管理功能，终端主机的增加删除修改", "allowed": true}, {"name": "安全检查", "desc": "安全检测功能，检查任务的管理，任务结果管理", "allowed": true}, {"name": "策略配置", "desc": "策略配置功能，检查策略的配置管理", "allowed": true}, {"name": "部门管理", "desc": "部门管理功能，部门信息的增加删除修改", "allowed": true}, {"name": "服务支持", "desc": "终端软件包下载管理", "allowed": true}]}, "supported_os": ["all"], "output_file": "trial_license.json", "register_host": true}