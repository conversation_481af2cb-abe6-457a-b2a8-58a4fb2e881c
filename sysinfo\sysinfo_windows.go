package sysinfo

import (
	"fmt"
	"log"

	"net"
	"os"
	"runtime"
	"sync"
	"time"

	"github.com/StackExchange/wmi"
	"github.com/shirou/gopsutil/v3/cpu"
	"github.com/shirou/gopsutil/v3/disk"
	"github.com/shirou/gopsutil/v3/host"
	"github.com/shirou/gopsutil/v3/mem"
	gnet "github.com/shirou/gopsutil/v3/net"
)

// CPU使用率缓存
var (
	cpuUsageCache float64
	lastCPUCheck  time.Time
	cpuCheckMu    sync.Mutex
)

// GetCPUUsage 获取CPU使用率
func GetCPUUsage() float64 {
	cpuCheckMu.Lock()
	defer cpuCheckMu.Unlock()

	// 如果缓存时间在2秒内，直接返回缓存值
	if time.Since(lastCPUCheck) < 2*time.Second && cpuUsageCache > 0 {
		return cpuUsageCache
	}

	percent, err := cpu.Percent(time.Second, false)
	if err != nil {
		log.Printf("获取CPU使用率失败: %v", err)
		return 0.0
	}

	if len(percent) > 0 {
		cpuUsageCache = percent[0]
		lastCPUCheck = time.Now()
		return cpuUsageCache
	}

	return 0.0
}

// GetMemoryUsage 获取内存使用率
func GetMemoryUsage() float64 {
	v, err := mem.VirtualMemory()
	if err != nil {
		log.Printf("获取内存使用率失败: %v", err)
		return 0.0
	}
	return v.UsedPercent
}

// GetDiskUsage 获取磁盘使用率
func GetDiskUsage() float64 {
	path := "/"
	if runtime.GOOS == "windows" {
		// 在Windows上，使用当前驱动器
		dir, err := os.Getwd()
		if err == nil && len(dir) >= 2 {
			path = dir[:2] // 取盘符部分，如 "C:"
		} else {
			path = "C:"
		}
	}

	usage, err := disk.Usage(path)
	if err != nil {
		log.Printf("获取磁盘使用率失败 %s: %v", path, err)
		return 0.0
	}
	return usage.UsedPercent
}

// GetOSInfo 获取操作系统信息
func GetOSInfo() string {
	info, err := host.Info()
	if err != nil {
		log.Printf("获取系统信息失败: %v", err)
		return fmt.Sprintf("%s (%s)", runtime.GOOS, runtime.GOARCH)
	}

	return fmt.Sprintf("%s %s (%s %s)",
		info.Platform, info.PlatformVersion,
		info.OS, runtime.GOARCH)
}

type Motherboard struct {
	Manufacturer string `json:"manufacturer"`
	Product      string `json:"product"`
	SerialNumber string
}

func GetMotherboardInfo() []Motherboard {
	var mb []Motherboard
	wmi.Query("SELECT Manufacturer, Product, SerialNumber FROM Win32_BaseBoard", &mb)
	for _, m := range mb {
		fmt.Printf("主板厂商: %s 型号: %s 序列号: %s\n", m.Manufacturer, m.Product, m.SerialNumber)
	}
	return mb
}

func HardInfo() {
	// 磁盘信息
	disks, _ := disk.Partitions(true)
	for _, d := range disks {
		fmt.Printf("磁盘分区: %s 挂载点: %s\n", d.Device, d.Mountpoint)
		fmt.Printf("%#v\n", d)
	}

	type DiskDrive struct {
		SerialNumber string
	}

	var disksex []DiskDrive
	wmi.Query("SELECT SerialNumber FROM Win32_DiskDrive", &disksex)
	for _, dd := range disksex {
		fmt.Printf("磁盘序列号: %s\n", dd.SerialNumber)
	}
	// // 显卡信息
	// var gpuList []Win32_VideoController
	// wmi.Query("SELECT * FROM Win32_VideoController", &gpuList)
	// for _, gpu := range gpuList {
	// 	fmt.Printf("显卡型号: %s\n", gpu.Name)
	// }

	// 网卡信息
	ifaces, _ := net.Interfaces()
	for _, iface := range ifaces {
		fmt.Printf("网卡: %s MAC: %s\n", iface.Name, iface.HardwareAddr)
	}

	// 主板信息
	// var mbList []Win32_BaseBoard
	// wmi.Query("SELECT * FROM Win32_BaseBoard", &mbList)
	// for _, mb := range mbList {
	// 	fmt.Printf("主板序列号: %s\n", mb.SerialNumber)
	// }

}

type NetworkAdapter struct {
	Caption          string
	MACAddress       string
	IPAddress        []string
	DefaultIPGateway []string
	Manufacturer     string `json:"manufacturer"`
	Name             string `json:"name"`
	Product          string
}

func GetNetworkByWMI() {
	var adapters []NetworkAdapter
	wmi.Query("SELECT Caption, MACAddress, IPAddress, DefaultIPGateway FROM Win32_NetworkAdapterConfiguration WHERE IPEnabled=True", &adapters)
	for _, a := range adapters {
		fmt.Printf("网卡: %s MAC: %s IP: %v 网关: %v\n", a.Caption, a.MACAddress, a.IPAddress, a.DefaultIPGateway)
	}

}

func GetNetworkInterfaces() {
	ifaces, _ := net.Interfaces()
	for _, iface := range ifaces {
		fmt.Printf("网卡名称: %s MAC地址: %s\n", iface.Name, iface.HardwareAddr)

		addrs, _ := iface.Addrs()
		for _, addr := range addrs {
			fmt.Printf("  IP地址: %s\n", addr)
		}
	}
}

type LogicalDisk struct {
	DeviceID   string // 磁盘标识符（如 C:）
	Size       uint64 // 总大小（字节）
	FreeSpace  uint64 // 可用空间（字节）
	FileSystem string // 文件系统类型
}

type DiskDrive struct {
	Model        string `json:"model"`        // 磁盘型号
	SerialNumber string `json:"serialNumber"` // 序列号
	Size         uint64 `json:"size"`         // 物理磁盘总大小（字节）
	Manufacturer string `json:"manufacturer"`
	Product      string `json:"product"`
}

func GetDiskDetails() {
	// 查询逻辑磁盘（分区信息）
	var disks []LogicalDisk
	wmi.Query("SELECT DeviceID, Size, FreeSpace, FileSystem FROM Win32_LogicalDisk WHERE DriveType=3", &disks)
	for _, d := range disks {
		fmt.Printf("【逻辑磁盘】%s | 总大小: %d GB | 可用: %d GB | 文件系统: %s\n",
			d.DeviceID, d.Size/1024/1024/1024, d.FreeSpace/1024/1024/1024, d.FileSystem)
	}

	// 查询物理磁盘（总容量）
	var physicalDisks []DiskDrive
	wmi.Query("SELECT Model, SerialNumber, Size FROM Win32_DiskDrive", &physicalDisks)
	for _, d := range physicalDisks {
		fmt.Printf("【物理磁盘】%s | 序列号: %s | 总大小: %d GB\n", d.Model, d.SerialNumber, d.Size/1024/1024/1024)
	}
}
func GetDiskInfo() {
	// 获取所有磁盘分区（包括挂载点、文件系统类型）
	partitions, _ := disk.Partitions(true)
	for _, p := range partitions {
		fmt.Printf("【分区】设备: %s | 挂载点: %s | 文件系统: %s\n", p.Device, p.Mountpoint, p.Fstype)

		// 获取分区使用详情（总大小、已用、可用）
		usage, _ := disk.Usage(p.Mountpoint)
		fmt.Printf("  总空间: %d GB | 已用: %d GB | 可用: %d GB | 使用率: %.2f%%\n",
			usage.Total/1024/1024/1024, usage.Used/1024/1024/1024, usage.Free/1024/1024/1024, usage.UsedPercent)
	}
}

// 磁盘结构体

// 显卡结构体
type VideoController struct {
	Name string `json:"name"`
}

// 网卡结构体

type NeedHostInfoForFinger struct {
	DiskArray        []DiskDrive       `json:"disk_array"`
	DisplayArray     []VideoController `json:"display_array"`
	AdapterArray     []NetworkAdapter  `json:"adapter_array"`
	MotherboardArray []Motherboard     `json:"motherboard_array"`
}

func AllInfoEx() NeedHostInfoForFinger {

	var needHostFinger NeedHostInfoForFinger
	// 查询磁盘信息
	var disks []DiskDrive
	wmi.Query("SELECT Manufacturer, Model, SerialNumber FROM Win32_DiskDrive", &disks)
	for _, d := range disks {
		fmt.Printf("【磁盘】厂商: %s | 型号: %s | 序列号: %s\n", d.Manufacturer, d.Model, d.SerialNumber)
		needHostFinger.DiskArray = append(needHostFinger.DiskArray, d)
	}

	// 查询显卡信息
	var gpus []VideoController
	wmi.Query("SELECT Name FROM Win32_VideoController", &gpus)
	for _, gpu := range gpus {
		fmt.Printf("【显卡】型号: %s\n", gpu.Name)
		needHostFinger.DisplayArray = append(needHostFinger.DisplayArray, gpu)
	}

	// 查询网卡信息
	var adapters []NetworkAdapter
	wmi.Query("SELECT Manufacturer, Name FROM Win32_NetworkAdapter WHERE NetConnectionID != NULL", &adapters)
	for _, a := range adapters {
		fmt.Printf("【网卡】厂商: %s | 型号: %s\n", a.Manufacturer, a.Name)
		needHostFinger.AdapterArray = append(needHostFinger.AdapterArray, a)
	}

	// 查询主板信息
	mb := GetMotherboardInfo()

	needHostFinger.MotherboardArray = append(needHostFinger.MotherboardArray, mb...)
	return needHostFinger
}

// 定义硬件信息结构体
type HostInfo struct {
	Manufacturer  string // 主板厂商
	ProductName   string // 主板型号
	OSName        string // 操作系统
	KernelVersion string // 内核版本
	Platform      string // 平台类型（Windows/Linux）
}

type CPUInfo struct {
	ModelName     string  // CPU型号
	PhysicalCores int     // 物理核心数
	LogicalCores  int     // 逻辑核心数
	UsagePercent  float64 // CPU使用率
}

type MemoryInfo struct {
	TotalGB     float64 // 总内存(GB)
	FreeGB      float64 // 空闲内存(GB)
	UsedPercent float64 // 使用率
}

type DiskInfo struct {
	Model       string  // 磁盘型号
	Serial      string  // 序列号
	SizeGB      float64 // 总容量(GB)
	UsedPercent float64 // 使用率
}

type NetworkInfo struct {
	Name      string // 网卡名称
	MAC       string // MAC地址
	IPv4      string // IPv4地址
	BytesSent uint64 // 发送字节数
	BytesRecv uint64 // 接收字节数
}

func AllHostInfo() {
	// 初始化信息结构体
	var hostInfo HostInfo
	var cpuInfo CPUInfo
	var memInfo MemoryInfo
	var disks []DiskInfo
	var nets []NetworkInfo

	// 1. 获取主机基础信息
	hostInfo = getHostInfoV1()

	// 2. 获取CPU信息
	cpuInfo = getCPUInfo()

	// 3. 获取内存信息
	memInfo = getMemoryInfo()

	// 4. 获取磁盘信息
	disks = getDiskInfo()

	// 5. 获取网卡信息
	nets = getNetworkInfo()

	// 格式化输出
	fmt.Println("========== 主机硬件信息 ==========")
	fmt.Printf("【主机】厂商: %s | 型号: %s | 系统: %s | 内核: %s\n",
		hostInfo.Manufacturer, hostInfo.ProductName, hostInfo.OSName, hostInfo.KernelVersion)
	fmt.Printf("【CPU】型号: %s | 物理核: %d | 逻辑核: %d | 使用率: %.1f%%\n",
		cpuInfo.ModelName, cpuInfo.PhysicalCores, cpuInfo.LogicalCores, cpuInfo.UsagePercent)
	fmt.Printf("【内存】总量: %.1f GB | 空闲: %.1f GB | 使用率: %.1f%%\n",
		memInfo.TotalGB, memInfo.FreeGB, memInfo.UsedPercent)
	fmt.Println("【磁盘】信息:")
	for _, d := range disks {
		fmt.Printf("  - 型号: %s | 序列: %s | 容量: %.1f GB | 使用率: %.1f%%\n",
			d.Model, d.Serial, d.SizeGB, d.UsedPercent)
	}
	fmt.Println("【网卡】信息:")
	for _, n := range nets {
		fmt.Printf("  - 名称: %s | MAC: %s | IPv4: %s | 流量: %d/%d bytes\n",
			n.Name, n.MAC, n.IPv4, n.BytesSent, n.BytesRecv)
	}
}

// 获取主机基础信息（主板、OS等）
func getHostInfoV1() HostInfo {
	var info HostInfo
	// 使用WMI查询主板信息

	// 获取系统信息
	sysInfo, _ := host.Info()
	info.OSName = sysInfo.OS
	info.KernelVersion = sysInfo.KernelVersion
	info.Platform = sysInfo.Platform

	return info
}

// 获取CPU信息
func getCPUInfo() CPUInfo {
	var info CPUInfo

	// 获取CPU型号
	cpuInfos, _ := cpu.Info()
	if len(cpuInfos) > 0 {
		fmt.Printf("%#v\n", cpuInfos)
	}

	// 获取CPU使用率
	percent, _ := cpu.Percent(time.Second, false)
	fmt.Printf("%#v\n", percent)

	return info
}

// 获取内存信息
func getMemoryInfo() MemoryInfo {
	var info MemoryInfo
	memInfo, _ := mem.VirtualMemory()
	info.TotalGB = float64(memInfo.Total) / 1024 / 1024 / 1024
	info.FreeGB = float64(memInfo.Free) / 1024 / 1024 / 1024
	info.UsedPercent = memInfo.UsedPercent

	return info
}

// 获取磁盘信息（型号、序列号、使用率）
func getDiskInfo() []DiskInfo {
	var disks []DiskInfo

	// 查询物理磁盘信息（WMI）
	var physicalDisks []DiskDrive
	wmi.Query("SELECT Model, SerialNumber, Size FROM Win32_DiskDrive", &physicalDisks)

	// 查询逻辑磁盘使用率（gopsutil）
	partitions, _ := disk.Partitions(true)
	for _, p := range partitions {
		usage, _ := disk.Usage(p.Mountpoint)
		// 匹配物理磁盘与逻辑分区
		for _, d := range physicalDisks {
			// if d.Model == p. && d.SerialNumber == p.SerialNumber {
			// 	disks = append(disks, DiskInfo{
			// 		Model:      d.Model,
			// 		Serial:     d.SerialNumber,
			// 		SizeGB:     float64(d.Size) / 1024 / 1024 / 1024,
			// 		UsedPercent: usage.UsedPercent,
			// 	})
			// 	break
			// }
			fmt.Printf("%#v\n", d)
		}
		fmt.Printf("%#v\n", usage)
	}

	return disks
}

// 获取网卡信息（名称、MAC、IP、流量）
func getNetworkInfo() []NetworkInfo {
	var nets []NetworkInfo

	// 获取所有网络接口
	interfaces, _ := net.Interfaces()
	for _, iface := range interfaces {
		addrs, _ := iface.Addrs()
		var ipv4 string
		for _, addr := range addrs {
			ipNet, ok := addr.(*net.IPNet)
			if ok && !ipNet.IP.IsLoopback() && ipNet.IP.To4() != nil {
				ipv4 = ipNet.IP.String()
				break
			}
		}

		// 获取网卡流量统计
		// stats, _ := gitnet(iface.Index)
		nets = append(nets, NetworkInfo{
			Name: iface.Name,
			MAC:  iface.HardwareAddr.String(),
			IPv4: ipv4,
			// BytesSent:  stats.BytesSent,
			// BytesRecv:  stats.BytesRecv,
		})
	}
	stats, err := gnet.IOCounters(true)
	if err != nil {
		fmt.Println("Error:", err)

	}

	for _, stat := range stats {
		fmt.Printf("网卡名称: %s\n", stat.Name)
		fmt.Printf("发送字节数: %d\n", stat.BytesSent)
		fmt.Printf("接收字节数: %d\n", stat.BytesRecv)
		fmt.Println("-------------------")
	}
	return nets
}
