package main

import (
	licenense "auth/Licenense"
	"fmt"
	"log"
)

func main() {
	fmt.Println("=== 测试 LicenseConfig 功能 ===\n")

	// 1. 测试创建默认配置模板
	fmt.Println("1. 创建配置模板:")
	
	// 创建试用版模板
	err := licenense.CreateConfigTemplate("trial", "trial_template.json")
	if err != nil {
		log.Printf("创建试用版模板失败: %v", err)
	} else {
		fmt.Println("✓ 试用版模板创建成功: trial_template.json")
	}
	
	// 创建正式版模板
	err = licenense.CreateConfigTemplate("full", "full_template.json")
	if err != nil {
		log.Printf("创建正式版模板失败: %v", err)
	} else {
		fmt.Println("✓ 正式版模板创建成功: full_template.json")
	}
	
	// 创建企业版模板
	err = licenense.CreateConfigTemplate("enterprise", "enterprise_template.json")
	if err != nil {
		log.Printf("创建企业版模板失败: %v", err)
	} else {
		fmt.Println("✓ 企业版模板创建成功: enterprise_template.json")
	}

	// 2. 测试加载和验证配置
	fmt.Println("\n2. 加载和验证配置:")
	
	config, err := licenense.LoadLicenseConfig("trial_template.json")
	if err != nil {
		log.Printf("加载配置失败: %v", err)
		return
	}
	
	err = licenense.ValidateLicenseConfig(config)
	if err != nil {
		log.Printf("配置验证失败: %v", err)
	} else {
		fmt.Println("✓ 配置验证通过")
	}

	// 3. 测试显示配置信息
	fmt.Println("\n3. 显示配置信息:")
	licenense.ShowLicenseConfig(config)

	// 4. 测试配置更新
	fmt.Println("\n4. 测试配置更新:")
	
	updates := map[string]interface{}{
		"user_name":            "更新的用户名",
		"company":              "更新的公司名",
		"trial_duration_days":  float64(60),
		"max_hosts":            float64(3),
	}
	
	err = licenense.UpdateLicenseConfig(config, updates)
	if err != nil {
		log.Printf("配置更新失败: %v", err)
	} else {
		fmt.Println("✓ 配置更新成功")
		fmt.Printf("  新用户名: %s\n", config.UserName)
		fmt.Printf("  新公司名: %s\n", config.Company)
		fmt.Printf("  新试用期: %d 天\n", config.TrialDurationDays)
		fmt.Printf("  新最大主机数: %d\n", config.MaxHosts)
	}

	// 5. 测试功能提取
	fmt.Println("\n5. 测试功能提取:")
	
	features := licenense.ExtractFeatureNames(config.Feature)
	fmt.Printf("提取的功能列表: %v\n", features)

	fmt.Println("\n=== 测试完成 ===")
}
