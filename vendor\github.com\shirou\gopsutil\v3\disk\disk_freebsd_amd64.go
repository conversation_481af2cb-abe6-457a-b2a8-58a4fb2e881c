// Created by cgo -god<PERSON><PERSON> - <PERSON>O NOT EDIT
// cgo -godefs types_freebsd.go

package disk

const (
	sizeofPtr        = 0x8
	sizeofShort      = 0x2
	sizeofInt        = 0x4
	sizeofLong       = 0x8
	sizeofLongLong   = 0x8
	sizeofLongDouble = 0x8

	devstat_NO_DATA = 0x00
	devstat_READ    = 0x01
	devstat_WRITE   = 0x02
	devstat_FREE    = 0x03
)

const (
	sizeOfdevstat = 0x120
)

type (
	_C_short       int16
	_C_int         int32
	_C_long        int64
	_C_long_long   int64
	_C_long_double int64
)

type devstat struct {
	Sequence0     uint32
	Allocated     int32
	Start_count   uint32
	End_count     uint32
	Busy_from     bintime
	Dev_links     _Ctype_struct___0
	Device_number uint32
	Device_name   [16]int8
	Unit_number   int32
	Bytes         [4]uint64
	Operations    [4]uint64
	Duration      [4]bintime
	Busy_time     bintime
	Creation_time bintime
	Block_size    uint32
	Pad_cgo_0     [4]byte
	Tag_types     [3]uint64
	Flags         uint32
	Device_type   uint32
	Priority      uint32
	Pad_cgo_1     [4]byte
	ID            *byte
	Sequence1     uint32
	Pad_cgo_2     [4]byte
}

type bintime struct {
	Sec  int64
	Frac uint64
}

type _Ctype_struct___0 struct {
	Empty uint64
}
