# Notes:
#   - Minimal appveyor.yml file is an empty file. All sections are optional.
#   - Indent each level of configuration with 2 spaces. Do not use tabs!
#   - All section names are case-sensitive.
#   - Section names should be unique on each level.

version: "1.3.0.{build}-alpha-{branch}"

os: Windows Server 2012 R2

branches:
  only:
    - master
    - v1.2
    - v1.1
    - v1.0

skip_tags: true

clone_folder: c:\gopath\src\github.com\go-ole\go-ole

environment:
  GOPATH: c:\gopath
  matrix:
  - GOARCH: amd64
    GOVERSION: 1.5
    GOROOT: c:\go
    DOWNLOADPLATFORM: "x64"

install:
  - choco install mingw
  - SET PATH=c:\tools\mingw64\bin;%PATH%
  # - Download COM Server
  - ps: Start-FileDownload "https://github.com/go-ole/test-com-server/releases/download/v1.0.2/test-com-server-${env:DOWNLOADPLATFORM}.zip"
  - 7z e test-com-server-%DOWNLOADPLATFORM%.zip -oc:\gopath\src\github.com\go-ole\go-ole > NUL
  - c:\gopath\src\github.com\go-ole\go-ole\build\register-assembly.bat
  # - set
  - go version
  - go env
  - go get -u golang.org/x/tools/cmd/cover
  - go get -u golang.org/x/tools/cmd/godoc
  - go get -u golang.org/x/tools/cmd/stringer

build_script:
  - cd c:\gopath\src\github.com\go-ole\go-ole
  - go get -v -t ./...
  - go build
  - go test -v -cover ./...

# disable automatic tests
test: off

# disable deployment
deploy: off
